import { Button } from "@/lib/forms";
import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Terms of Service - Shopper POS",
  description: "Terms of Service for Shopper POS",
};

export default function TermsOfServicePage() {
  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
          <svg
            className="h-6 w-6 text-blue-600 dark:text-blue-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
            />
          </svg>
        </div>
        <h1 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Terms of Service
        </h1>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Last updated: {new Date().toLocaleDateString()}
        </p>
      </div>

      {/* Terms Content */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            1. Acceptance of Terms
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            By accessing and using Shopper POS ("the Service"), you accept and agree to be bound by the terms and provision of this agreement.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            2. Description of Service
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Shopper POS is a point-of-sale system designed for supermarkets and retail businesses. The Service provides inventory management, sales tracking, and business analytics tools.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            3. User Accounts
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You are responsible for maintaining the confidentiality of your account and password. You agree to accept responsibility for all activities that occur under your account.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            4. Acceptable Use
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You agree not to use the Service for any unlawful purpose or in any way that could damage, disable, overburden, or impair the Service.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            5. Data and Privacy
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the Service, to understand our practices.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            6. Intellectual Property
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            The Service and its original content, features, and functionality are owned by Shopper POS and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            7. Termination
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We may terminate or suspend your account and bar access to the Service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            8. Limitation of Liability
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            In no event shall Shopper POS, nor its directors, employees, partners, agents, suppliers, or affiliates, be liable for any indirect, incidental, special, consequential, or punitive damages.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            9. Changes to Terms
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            10. Contact Information
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            If you have any questions about these Terms of Service, please contact <NAME_EMAIL>.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Link href="/register" className="flex-1">
            <Button variant="primary" className="w-full">
              I Accept - Continue to Register
            </Button>
          </Link>
          <Link href="/login" className="flex-1">
            <Button variant="secondary" className="w-full">
              Back to Sign In
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
