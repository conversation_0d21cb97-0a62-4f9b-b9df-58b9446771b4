import { Button } from "@/lib/forms";
import { Metadata } from "next";
import Link from "next/link";

export const metadata: Metadata = {
  title: "Privacy Policy - Shopper POS",
  description: "Privacy Policy for Shopper POS",
};

export default function PrivacyPolicyPage() {
  return (
    <div className="w-full space-y-8">
      {/* Header */}
      <div className="text-center">
        <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900">
          <svg
            className="h-6 w-6 text-blue-600 dark:text-blue-400"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
            />
          </svg>
        </div>
        <h1 className="mt-6 text-3xl font-extrabold text-gray-900 dark:text-white">
          Privacy Policy
        </h1>
        <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
          Last updated: {new Date().toLocaleDateString()}
        </p>
      </div>

      {/* Privacy Content */}
      <div className="bg-white dark:bg-gray-800 shadow rounded-lg p-6 space-y-6">
        <div className="prose dark:prose-invert max-w-none">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            1. Information We Collect
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We collect information you provide directly to us, such as when you create an account, use our services, or contact us for support. This may include your name, email address, business information, and usage data.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            2. How We Use Your Information
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We use the information we collect to provide, maintain, and improve our services, process transactions, send you technical notices and support messages, and communicate with you about products, services, and promotional offers.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            3. Information Sharing
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share information with trusted service providers who assist us in operating our platform.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            4. Data Security
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the internet is 100% secure.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            5. Data Retention
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We retain your information for as long as your account is active or as needed to provide you services. We will retain and use your information as necessary to comply with our legal obligations and resolve disputes.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            6. Your Rights
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            You have the right to access, update, or delete your personal information. You may also opt out of certain communications from us. To exercise these rights, please contact us using the information provided below.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            7. Cookies and Tracking
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We use cookies and similar tracking technologies to collect and use personal information about you. You can control cookies through your browser settings, but disabling cookies may affect the functionality of our service.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            8. Third-Party Services
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Our service may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties. We encourage you to read their privacy policies.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            9. Children's Privacy
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Our service is not intended for children under 13 years of age. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            10. Changes to This Policy
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            We may update this privacy policy from time to time. We will notify you of any changes by posting the new policy on this page and updating the "Last updated" date.
          </p>

          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            11. Contact Us
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            If you have any questions about this Privacy Policy, please contact <NAME_EMAIL> or through our support channels.
          </p>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-gray-200 dark:border-gray-700">
          <Link href="/register" className="flex-1">
            <Button variant="primary" className="w-full">
              I Accept - Continue to Register
            </Button>
          </Link>
          <Link href="/login" className="flex-1">
            <Button variant="secondary" className="w-full">
              Back to Sign In
            </Button>
          </Link>
        </div>
      </div>
    </div>
  );
}
