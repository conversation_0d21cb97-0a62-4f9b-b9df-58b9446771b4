import Image from "next/image";

export default function AuthLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <main className="font-inter flex min-h-screen w-full">
      {/* Auth Form Section */}
      <div className="flex flex-1 flex-col justify-center px-4 py-12 sm:px-6 lg:flex-none lg:px-20 xl:px-24">
        <div className="mx-auto w-full max-w-sm lg:w-96">{children}</div>
      </div>

      {/* Background Image Section */}
      <div className="auth-asset">
        <div className="relative h-full w-full">
          <Image
            src="/images/login-background.png"
            alt="Shopper POS - Modern point of sale system"
            fill
            className="object-cover"
            priority
          />
          {/* Overlay with branding */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent">
            <div className="flex h-full flex-col justify-end p-8 text-white">
              <div className="space-y-4">
                <h2 className="text-3xl font-bold">Welcome to Shopper POS</h2>
                <p className="text-lg text-gray-200">
                  Modern point of sale system designed for supermarkets and
                  retail businesses.
                </p>
                <div className="flex items-center space-x-4 text-sm text-gray-300">
                  <span>✓ Inventory Management</span>
                  <span>✓ Sales Analytics</span>
                  <span>✓ Multi-user Support</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </main>
  );
}
