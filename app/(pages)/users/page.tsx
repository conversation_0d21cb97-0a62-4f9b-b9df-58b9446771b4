"use client";

import Link from "next/link";
import { useState } from "react";
import {
  Button,
  FormCard,
  FormGrid,
  SearchInput,
  Select,
} from "../../../lib/forms";

// Mock user data
const users = [
  {
    id: 1,
    name: "Organic Bananas",
    category: "Fruits",
    price: 2.99,
    stock: 150,
    sku: "ORG-BAN-001",
    status: "In Stock",
  },
  {
    id: 2,
    name: "Whole Milk",
    category: "Dairy",
    price: 3.49,
    stock: 45,
    sku: "MILK-WHL-001",
    status: "In Stock",
  },
  {
    id: 3,
    name: "Bread - Whole Wheat",
    category: "Bakery",
    price: 2.79,
    stock: 8,
    sku: "BRD-WHT-001",
    status: "Low Stock",
  },
  {
    id: 4,
    name: "Ground Coffee",
    category: "Beverages",
    price: 12.99,
    stock: 0,
    sku: "COF-GRD-001",
    status: "Out of Stock",
  },
];

export default function UsersPage() {
  const [filters, setFilters] = useState({
    search: "",
    category: "",
    status: "",
  });

  const categoryOptions = [
    { value: "", label: "All Categories" },
    { value: "fruits", label: "Fruits" },
    { value: "dairy", label: "Dairy" },
    { value: "bakery", label: "Bakery" },
    { value: "beverages", label: "Beverages" },
    { value: "meat", label: "Meat & Poultry" },
    { value: "frozen", label: "Frozen Foods" },
  ];

  const statusOptions = [
    { value: "", label: "All Status" },
    { value: "in-stock", label: "In Stock" },
    { value: "low-stock", label: "Low Stock" },
    { value: "out-of-stock", label: "Out of Stock" },
  ];

  const handleSearch = (query: string) => {
    setFilters((prev) => ({ ...prev, search: query }));
    console.log("Searching for:", query);
  };

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
              Users
            </h1>
            <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
              Manage your user inventory and pricing
            </p>
          </div>
          <Link href="/users/new">
            <Button
              variant="primary"
              icon={
                <svg
                  className="h-5 w-5"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              }
            >
              Add Product
            </Button>
          </Link>
        </div>
      </div>

      {/* Search and Filters */}
      <FormCard title="Search & Filter Users">
        <FormGrid cols={3} gap="md">
          <SearchInput
            name="search"
            label="Search Users"
            placeholder="Search by name or SKU..."
            value={filters.search}
            onSearch={handleSearch}
            debounceMs={300}
            showClearButton
          />

          <Select
            name="category"
            label="Category"
            options={categoryOptions}
            value={filters.category}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, category: e.target.value }))
            }
          />

          <Select
            name="status"
            label="Stock Status"
            options={statusOptions}
            value={filters.status}
            onChange={(e) =>
              setFilters((prev) => ({ ...prev, status: e.target.value }))
            }
          />
        </FormGrid>
      </FormCard>

      {/* Users Table */}
      <div className="overflow-hidden rounded-lg bg-white shadow dark:bg-gray-800">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Product
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Category
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Price
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Stock
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase dark:text-gray-300">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-800">
              {users.map((user) => (
                <tr
                  key={user.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700"
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div className="text-sm font-medium text-gray-900 dark:text-white">
                        {user.name}
                      </div>
                      <div className="text-sm text-gray-500 dark:text-gray-400">
                        SKU: {user.sku}
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    {user.category}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    ${user.price}
                  </td>
                  <td className="px-6 py-4 text-sm whitespace-nowrap text-gray-900 dark:text-white">
                    {user.stock}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span
                      className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${
                        user.status === "In Stock"
                          ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                          : user.status === "Low Stock"
                            ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-800 dark:text-yellow-100"
                            : "bg-red-100 text-red-800 dark:bg-red-800 dark:text-red-100"
                      }`}
                    >
                      {user.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 text-sm font-medium whitespace-nowrap">
                    <Link
                      href={`/users/${user.id}`}
                      className="mr-4 text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      Edit
                    </Link>
                    <button className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300">
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
