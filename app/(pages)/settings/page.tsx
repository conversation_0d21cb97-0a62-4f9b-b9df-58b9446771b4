import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Settings - Shopper POS",
  description: "Configure your POS system settings",
};

export default function SettingsPage() {
  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="border-b border-gray-200 pb-4 dark:border-gray-700">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Settings
        </h1>
        <p className="mt-1 text-sm text-gray-600 dark:text-gray-400">
          Configure your POS system and business settings
        </p>
      </div>

      {/* Settings Sections */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Business Information */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 mb-6 dark:text-white">
            Business Information
          </h3>
          <div className="space-y-4">
            <div>
              <label htmlFor="businessName" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Business Name
              </label>
              <input
                type="text"
                name="businessName"
                id="businessName"
                defaultValue="Shopper Supermarket"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Address
              </label>
              <textarea
                id="address"
                name="address"
                rows={3}
                defaultValue="123 Main Street, City, State 12345"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Phone
                </label>
                <input
                  type="tel"
                  name="phone"
                  id="phone"
                  defaultValue="(*************"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Email
                </label>
                <input
                  type="email"
                  name="email"
                  id="email"
                  defaultValue="<EMAIL>"
                  className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Tax Settings */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 mb-6 dark:text-white">
            Tax Settings
          </h3>
          <div className="space-y-4">
            <div>
              <label htmlFor="taxRate" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Default Tax Rate (%)
              </label>
              <input
                type="number"
                name="taxRate"
                id="taxRate"
                step="0.01"
                defaultValue="8.25"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label htmlFor="taxNumber" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Tax ID Number
              </label>
              <input
                type="text"
                name="taxNumber"
                id="taxNumber"
                defaultValue="12-3456789"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div className="flex items-center">
              <input
                id="includeTax"
                name="includeTax"
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="includeTax" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Include tax in product prices
              </label>
            </div>
          </div>
        </div>

        {/* Receipt Settings */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 mb-6 dark:text-white">
            Receipt Settings
          </h3>
          <div className="space-y-4">
            <div>
              <label htmlFor="receiptHeader" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Receipt Header
              </label>
              <textarea
                id="receiptHeader"
                name="receiptHeader"
                rows={2}
                defaultValue="Thank you for shopping with us!"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div>
              <label htmlFor="receiptFooter" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                Receipt Footer
              </label>
              <textarea
                id="receiptFooter"
                name="receiptFooter"
                rows={2}
                defaultValue="Visit us again soon!"
                className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
              />
            </div>
            <div className="flex items-center">
              <input
                id="printReceipt"
                name="printReceipt"
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="printReceipt" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Auto-print receipts
              </label>
            </div>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
          <h3 className="text-lg font-medium text-gray-900 mb-6 dark:text-white">
            Payment Methods
          </h3>
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                id="cash"
                name="cash"
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="cash" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Cash
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="creditCard"
                name="creditCard"
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="creditCard" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Credit Card
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="debitCard"
                name="debitCard"
                type="checkbox"
                defaultChecked
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="debitCard" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Debit Card
              </label>
            </div>
            <div className="flex items-center">
              <input
                id="mobilePayment"
                name="mobilePayment"
                type="checkbox"
                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <label htmlFor="mobilePayment" className="ml-2 block text-sm text-gray-900 dark:text-white">
                Mobile Payment (Apple Pay, Google Pay)
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* System Settings */}
      <div className="bg-white shadow rounded-lg p-6 dark:bg-gray-800">
        <h3 className="text-lg font-medium text-gray-900 mb-6 dark:text-white">
          System Settings
        </h3>
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <div>
            <label htmlFor="currency" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Currency
            </label>
            <select
              id="currency"
              name="currency"
              defaultValue="USD"
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="USD">USD - US Dollar</option>
              <option value="EUR">EUR - Euro</option>
              <option value="GBP">GBP - British Pound</option>
              <option value="CAD">CAD - Canadian Dollar</option>
            </select>
          </div>
          <div>
            <label htmlFor="timezone" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Timezone
            </label>
            <select
              id="timezone"
              name="timezone"
              defaultValue="America/New_York"
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="America/New_York">Eastern Time</option>
              <option value="America/Chicago">Central Time</option>
              <option value="America/Denver">Mountain Time</option>
              <option value="America/Los_Angeles">Pacific Time</option>
            </select>
          </div>
          <div>
            <label htmlFor="language" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
              Language
            </label>
            <select
              id="language"
              name="language"
              defaultValue="en"
              className="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 sm:text-sm dark:bg-gray-700 dark:border-gray-600 dark:text-white"
            >
              <option value="en">English</option>
              <option value="es">Spanish</option>
              <option value="fr">French</option>
              <option value="de">German</option>
            </select>
          </div>
        </div>
      </div>

      {/* Save Button */}
      <div className="flex justify-end">
        <button className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
          <svg className="-ml-1 mr-2 h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
          </svg>
          Save Settings
        </button>
      </div>
    </div>
  );
}
